// Partnership Agreement PDF Generator
// Specialized PDF generation for Partnership Agreements

import { generateContractPdf } from './contractPdfGenerator';

/**
 * Generate PDF specifically for Partnership Agreements
 * @param {string} contractContent - The partnership agreement content
 * @param {Object} formData - Form data containing partnership agreement details
 * @param {string} language - Language for RTL/LTR support
 */
export const generatePartnershipAgreementPdf = async (contractContent, formData, language = 'English') => {
    // Format content with Partnership Agreement specific tags
    const formattedContent = formatPartnershipAgreementContent(contractContent, formData);
    
    return await generateContractPdf(formattedContent, 'partnership', formData, language);
};

/**
 * Format partnership agreement content with proper tags for PDF rendering
 */
const formatPartnershipAgreementContent = (content, formData) => {
    let formattedContent = content;
    
    // Add partnership agreement specific formatting
    formattedContent = formattedContent
        // Main title
        .replace(/^(PARTNERSHIP AGREEMENT|Partnership Agreement)/gm, '~H~$1')
        // Section headers
        .replace(/^(\d+\.\s*[A-Z][^.]*:?)$/gm, '~SECTION~$1')
        // Sub-sections
        .replace(/^([a-z]\)\s*[A-Z][^.]*:?)$/gm, '~H_SUB~$1')
        // Partnership items
        .replace(/^(\s*[-•]\s*.+)$/gm, '~L~$1')
        // Important clauses
        .replace(/^(WHEREAS|NOW THEREFORE|IN WITNESS WHEREOF)/gm, '~CLAUSE~$1')
        // Capital contributions
        .replace(/(Capital Contributions?:?)/gi, '~S~$1')
        // Profit sharing
        .replace(/(Profit Sharing|Distribution of Profits?):?/gi, '~S~$1')
        // Management structure
        .replace(/(Management|Authority|Decision Making):?/gi, '~S~$1')
        // Partnership duties
        .replace(/(Duties|Responsibilities|Obligations):?/gi, '~S~$1')
        // Dissolution
        .replace(/(Dissolution|Termination):?/gi, '~S~$1')
        // Dispute resolution
        .replace(/(Dispute Resolution|Arbitration):?/gi, '~S~$1')
        // Withdrawal
        .replace(/(Withdrawal|Exit|Retirement):?/gi, '~S~$1')
        // Books and records
        .replace(/(Books and Records|Accounting):?/gi, '~S~$1')
        // Governing Law
        .replace(/(Governing Law|Jurisdiction):?/gi, '~S~$1');

    // Add partnership-specific metadata
    const partnershipMetadata = generatePartnershipMetadata(formData);
    formattedContent = partnershipMetadata + '\n\n' + formattedContent;
    
    return formattedContent;
};

/**
 * Generate partnership agreement metadata section
 */
const generatePartnershipMetadata = (formData) => {
    const metadata = [];
    
    if (formData.partnershipName) {
        metadata.push(`~P~Partnership Name: ${formData.partnershipName}`);
    }
    
    if (formData.partner1Name) {
        metadata.push(`~P~Partner 1: ${formData.partner1Name}`);
    }
    
    if (formData.partner2Name) {
        metadata.push(`~P~Partner 2: ${formData.partner2Name}`);
    }
    
    if (formData.businessPurpose) {
        metadata.push(`~P~Business Purpose: ${formData.businessPurpose}`);
    }
    
    if (formData.partnershipType) {
        metadata.push(`~P~Partnership Type: ${formData.partnershipType}`);
    }
    
    if (formData.initialCapital) {
        metadata.push(`~P~Initial Capital: ${formData.initialCapital}`);
    }
    
    if (formData.profitSharingRatio) {
        metadata.push(`~P~Profit Sharing Ratio: ${formData.profitSharingRatio}`);
    }
    
    if (formData.partnershipDuration) {
        metadata.push(`~P~Partnership Duration: ${formData.partnershipDuration}`);
    }
    
    return metadata.join('\n');
};

/**
 * Partnership Agreement specific styling options
 */
export const partnershipAgreementPdfOptions = {
    title: 'Partnership Agreement',
    primaryColor: '#7c3aed', // Purple for partnership
    secondaryColor: '#059669', // Success green
    accentColor: '#dc2626', // Red for important clauses
    fontFamily: 'Georgia',
    fontSize: {
        title: 20,
        heading: 16,
        subheading: 14,
        body: 11,
        small: 9
    },
    margins: {
        top: 20,
        bottom: 20,
        left: 15,
        right: 15
    },
    spacing: {
        afterTitle: 15,
        afterHeading: 12,
        afterSubheading: 10,
        afterParagraph: 8,
        afterListItem: 6
    }
};
