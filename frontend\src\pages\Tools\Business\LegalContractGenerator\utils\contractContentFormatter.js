// Contract Content Formatter
// Adds formatting tags to contract content for enhanced display and PDF generation

/**
 * Format contract content with proper tags for enhanced display
 * @param {string} contractContent - Raw contract content
 * @param {string} contractType - Type of contract (service, partnership)
 * @param {Object} formData - Form data containing contract details
 * @returns {string} Formatted contract content with tags
 */
export const formatContractContent = (contractContent, contractType, formData) => {
    if (!contractContent) return '';
    
    let formattedContent = contractContent;
    
    // Apply general formatting rules
    formattedContent = applyGeneralFormatting(formattedContent);
    
    // Apply contract-type specific formatting
    switch (contractType) {
        case 'service':
            formattedContent = applyServiceAgreementFormatting(formattedContent);
            break;
        case 'partnership':
            formattedContent = applyPartnershipAgreementFormatting(formattedContent);
            break;
        default:
            formattedContent = applyDefaultContractFormatting(formattedContent);
    }
    
    // Add signature blocks if not present
    formattedContent = addSignatureBlocks(formattedContent, contractType, formData);
    
    return formattedContent;
};

/**
 * Apply general formatting rules to all contracts
 */
const applyGeneralFormatting = (content) => {
    return content
        // Main contract titles
        .replace(/^(.*AGREEMENT.*|.*CONTRACT.*)$/gmi, '~H~$1')
        // Section numbers (1., 2., etc.)
        .replace(/^(\d+\.\s*[A-Z][^.]*\.?)$/gm, '~SECTION~$1')
        // Sub-sections (a), b), etc.)
        .replace(/^([a-z]\)\s*[A-Z][^.]*\.?)$/gm, '~H_SUB~$1')
        // Numbered sub-sections (1.1, 1.2, etc.)
        .replace(/^(\d+\.\d+\s*[A-Z][^.]*\.?)$/gm, '~H_SUB~$1')
        // Important legal clauses
        .replace(/^(WHEREAS|NOW THEREFORE|IN WITNESS WHEREOF|WITNESSETH)/gm, '~CLAUSE~$1')
        // List items starting with bullet points or dashes
        .replace(/^(\s*[-•*]\s*.+)$/gm, '~L~$1')
        // Definitions section
        .replace(/(DEFINITIONS?:?)/gi, '~S~$1')
        // Terms and conditions
        .replace(/(TERMS AND CONDITIONS?:?)/gi, '~S~$1')
        // Governing law
        .replace(/(GOVERNING LAW:?)/gi, '~S~$1')
        // Entire agreement
        .replace(/(ENTIRE AGREEMENT:?)/gi, '~S~$1')
        // Amendments
        .replace(/(AMENDMENTS?:?)/gi, '~S~$1')
        // Severability
        .replace(/(SEVERABILITY:?)/gi, '~S~$1')
        // Force majeure
        .replace(/(FORCE MAJEURE:?)/gi, '~S~$1')
        // Notices
        .replace(/(NOTICES?:?)/gi, '~S~$1')
        // Counterparts
        .replace(/(COUNTERPARTS?:?)/gi, '~S~$1')
        // Electronic signatures
        .replace(/(ELECTRONIC SIGNATURES?:?)/gi, '~S~$1')
        // Regular paragraphs (not already tagged)
        .replace(/^([A-Z][^~\n]*[.!?])$/gm, '~P~$1');
};

/**
 * Apply Service Agreement specific formatting
 */
const applyServiceAgreementFormatting = (content) => {
    return content
        // Service-specific sections
        .replace(/(SCOPE OF SERVICES?:?)/gi, '~S~$1')
        .replace(/(SERVICE DESCRIPTION:?)/gi, '~S~$1')
        .replace(/(DELIVERABLES?:?)/gi, '~S~$1')
        .replace(/(TIMELINE:?|SCHEDULE:?|DURATION:?)/gi, '~S~$1')
        .replace(/(PAYMENT TERMS?:?)/gi, '~S~$1')
        .replace(/(FEES?:?|COMPENSATION:?)/gi, '~S~$1')
        .replace(/(EXPENSES?:?)/gi, '~S~$1')
        .replace(/(INTELLECTUAL PROPERTY:?|IP RIGHTS?:?)/gi, '~S~$1')
        .replace(/(CONFIDENTIALITY:?|NON-DISCLOSURE:?)/gi, '~S~$1')
        .replace(/(WARRANTIES?:?|REPRESENTATIONS?:?)/gi, '~S~$1')
        .replace(/(LIMITATION OF LIABILITY:?|LIABILITY:?)/gi, '~S~$1')
        .replace(/(INDEMNIFICATION:?)/gi, '~S~$1')
        .replace(/(TERMINATION:?)/gi, '~S~$1')
        .replace(/(INDEPENDENT CONTRACTOR:?)/gi, '~S~$1')
        .replace(/(NON-COMPETE:?|NON-SOLICITATION:?)/gi, '~S~$1');
};

/**
 * Apply Partnership Agreement specific formatting
 */
const applyPartnershipAgreementFormatting = (content) => {
    return content
        // Partnership-specific sections
        .replace(/(PARTNERSHIP NAME:?|BUSINESS NAME:?)/gi, '~S~$1')
        .replace(/(BUSINESS PURPOSE:?|PURPOSE:?)/gi, '~S~$1')
        .replace(/(PARTNERSHIP TERM:?|DURATION:?)/gi, '~S~$1')
        .replace(/(CAPITAL CONTRIBUTIONS?:?)/gi, '~S~$1')
        .replace(/(PROFIT SHARING:?|DISTRIBUTION OF PROFITS?:?)/gi, '~S~$1')
        .replace(/(LOSS SHARING:?|DISTRIBUTION OF LOSSES?:?)/gi, '~S~$1')
        .replace(/(MANAGEMENT:?|AUTHORITY:?|DECISION MAKING:?)/gi, '~S~$1')
        .replace(/(DUTIES:?|RESPONSIBILITIES?:?|OBLIGATIONS?:?)/gi, '~S~$1')
        .replace(/(BOOKS AND RECORDS:?|ACCOUNTING:?)/gi, '~S~$1')
        .replace(/(BANKING:?|FINANCIAL ACCOUNTS?:?)/gi, '~S~$1')
        .replace(/(WITHDRAWAL:?|EXIT:?|RETIREMENT:?)/gi, '~S~$1')
        .replace(/(DISSOLUTION:?|TERMINATION:?)/gi, '~S~$1')
        .replace(/(DISPUTE RESOLUTION:?|ARBITRATION:?)/gi, '~S~$1')
        .replace(/(ADMISSION OF NEW PARTNERS?:?)/gi, '~S~$1')
        .replace(/(DEATH OR DISABILITY:?)/gi, '~S~$1')
        .replace(/(NON-COMPETE:?|RESTRICTIVE COVENANTS?:?)/gi, '~S~$1');
};

/**
 * Apply default contract formatting for other contract types
 */
const applyDefaultContractFormatting = (content) => {
    return content
        .replace(/(PARTIES:?)/gi, '~S~$1')
        .replace(/(RECITALS:?)/gi, '~S~$1')
        .replace(/(CONSIDERATION:?)/gi, '~S~$1')
        .replace(/(PERFORMANCE:?)/gi, '~S~$1')
        .replace(/(DEFAULT:?|BREACH:?)/gi, '~S~$1')
        .replace(/(REMEDIES:?)/gi, '~S~$1')
        .replace(/(DISPUTE RESOLUTION:?)/gi, '~S~$1');
};

/**
 * Add signature blocks to the contract if not present
 */
const addSignatureBlocks = (content, contractType, formData) => {
    // Check if signature blocks already exist
    if (content.includes('~SIGNATURE~') || content.includes('SIGNATURE') || content.includes('IN WITNESS WHEREOF')) {
        return content;
    }
    
    let signatureSection = '\n\n~CLAUSE~IN WITNESS WHEREOF, the parties have executed this agreement as of the date first written above.\n\n';
    
    // Add party-specific signature blocks based on contract type
    if (contractType === 'service') {
        signatureSection += '~SIGNATURE~Service Provider\n\n';
        signatureSection += '~SIGNATURE~Client\n\n';
    } else if (contractType === 'partnership') {
        signatureSection += '~SIGNATURE~Partner 1\n\n';
        signatureSection += '~SIGNATURE~Partner 2\n\n';
        if (formData.partner3Name) {
            signatureSection += '~SIGNATURE~Partner 3\n\n';
        }
    } else {
        signatureSection += '~SIGNATURE~Party 1\n\n';
        signatureSection += '~SIGNATURE~Party 2\n\n';
    }
    
    // Add witness and notary blocks if required by jurisdiction
    if (formData.requiresWitness) {
        signatureSection += '~WITNESS~\n\n';
    }
    
    if (formData.requiresNotarization) {
        signatureSection += '~NOTARY~\n\n';
    }
    
    return content + signatureSection;
};

/**
 * Clean contract content for plain text export
 */
export const cleanContractContent = (formattedContent) => {
    return formattedContent
        .replace(/~[A-Z_]+~/g, '') // Remove all formatting tags
        .replace(/\n{3,}/g, '\n\n') // Normalize line breaks
        .trim();
};

/**
 * Extract contract metadata for PDF generation
 */
export const extractContractMetadata = (formData, contractType) => {
    const metadata = {
        title: formData.contractTitle || getDefaultTitle(contractType),
        parties: extractParties(formData, contractType),
        date: formData.effectiveDate || new Date().toLocaleDateString(),
        jurisdiction: formData.jurisdiction || 'General Jurisdiction',
        contractType: contractType
    };
    
    return metadata;
};

/**
 * Get default title based on contract type
 */
const getDefaultTitle = (contractType) => {
    const titles = {
        service: 'Service Agreement',
        partnership: 'Partnership Agreement',
        nda: 'Non-Disclosure Agreement',
        freelance: 'Freelance Agreement',
        supplier: 'Supplier Agreement',
        employment: 'Employment Contract',
        lease: 'Lease Agreement'
    };
    
    return titles[contractType] || 'Legal Contract';
};

/**
 * Extract party information based on contract type
 */
const extractParties = (formData, contractType) => {
    const parties = [];
    
    if (contractType === 'service') {
        if (formData.serviceProvider) parties.push(formData.serviceProvider);
        if (formData.client) parties.push(formData.client);
    } else if (contractType === 'partnership') {
        if (formData.partner1Name) parties.push(formData.partner1Name);
        if (formData.partner2Name) parties.push(formData.partner2Name);
        if (formData.partner3Name) parties.push(formData.partner3Name);
    } else {
        if (formData.party1Name) parties.push(formData.party1Name);
        if (formData.party2Name) parties.push(formData.party2Name);
    }
    
    return parties;
};
