import React, { useCallback } from 'react';
import { FiDollar<PERSON>ign, FiPieChart, FiTrendingUp, FiCalendar } from 'react-icons/fi';
import { FormInput, FormSelect, FormTextarea, SectionHeader } from '../../components/FormComponents';

const PartnershipFinancialStep = ({ formData, onFormDataChange, validationErrors }) => {
  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    onFormDataChange({ [name]: value });
  }, [onFormDataChange]);

  return (
    <div className="space-y-8">
      {/* Capital Contributions */}
      <div>
        <SectionHeader 
          title="Capital Contributions" 
          description="Initial investments and contributions by each partner"
          icon={FiDollarSign}
        />
        <div className="grid md:grid-cols-2 gap-6">
          <FormInput
            id="partnerOneContribution"
            label="Partner 1 Contribution"
            type="number"
            placeholder="50000"
            value={formData.partnerOneContribution}
            onChange={handleChange}
            helpText="Initial capital contribution amount (USD)"
            tooltip="The monetary value of the initial investment or contribution made by Partner 1."
            error={validationErrors.partnerOneContribution}
            required
          />
          <FormInput
            id="partnerTwoContribution"
            label="Partner 2 Contribution"
            type="number"
            placeholder="50000"
            value={formData.partnerTwoContribution}
            onChange={handleChange}
            helpText="Initial capital contribution amount (USD)"
            tooltip="The monetary value of the initial investment or contribution made by Partner 2."
            error={validationErrors.partnerTwoContribution}
            required
          />
        </div>
        <div className="grid md:grid-cols-2 gap-6 mt-4">
          <FormSelect
            id="contributionType"
            label="Contribution Type"
            value={formData.contributionType}
            onChange={handleChange}
            helpText="Type of contributions being made"
            tooltip="Specify whether contributions are cash, assets, services, or a combination."
            error={validationErrors.contributionType}
          >
            <option value="">Select Type</option>
            <option value="cash">Cash Only</option>
            <option value="assets">Assets Only</option>
            <option value="services">Services/Labor</option>
            <option value="mixed">Cash and Assets</option>
            <option value="other">Other (specify in additional terms)</option>
          </FormSelect>
          <FormInput
            id="additionalContributions"
            label="Additional Contributions"
            placeholder="Future contribution requirements..."
            value={formData.additionalContributions}
            onChange={handleChange}
            helpText="Any future or additional contribution requirements"
            tooltip="Describe any requirements for additional capital contributions in the future."
            maxLength={200}
            error={validationErrors.additionalContributions}
          />
        </div>
      </div>

      {/* Profit and Loss Sharing */}
      <div>
        <SectionHeader 
          title="Profit and Loss Sharing" 
          description="How profits and losses will be distributed"
          icon={FiPieChart}
        />
        <div className="grid md:grid-cols-2 gap-6">
          <FormInput
            id="partnerOneProfitShare"
            label="Partner 1 Profit Share (%)"
            type="number"
            placeholder="50"
            min="0"
            max="100"
            value={formData.partnerOneProfitShare}
            onChange={handleChange}
            helpText="Percentage of profits allocated to Partner 1"
            tooltip="Enter the percentage (0-100) of profits that Partner 1 will receive."
            error={validationErrors.partnerOneProfitShare}
            required
          />
          <FormInput
            id="partnerTwoProfitShare"
            label="Partner 2 Profit Share (%)"
            type="number"
            placeholder="50"
            min="0"
            max="100"
            value={formData.partnerTwoProfitShare}
            onChange={handleChange}
            helpText="Percentage of profits allocated to Partner 2"
            tooltip="Enter the percentage (0-100) of profits that Partner 2 will receive. Total should equal 100%."
            error={validationErrors.partnerTwoProfitShare}
            required
          />
        </div>
        <div className="grid md:grid-cols-2 gap-6 mt-4">
          <FormSelect
            id="lossDistribution"
            label="Loss Distribution"
            value={formData.lossDistribution}
            onChange={handleChange}
            helpText="How losses will be distributed between partners"
            tooltip="Specify how business losses will be shared between the partners."
            error={validationErrors.lossDistribution}
          >
            <option value="">Select Distribution Method</option>
            <option value="same_as_profit">Same as Profit Sharing</option>
            <option value="equal">Equal Distribution (50/50)</option>
            <option value="contribution_based">Based on Capital Contributions</option>
            <option value="custom">Custom (specify in additional terms)</option>
          </FormSelect>
          <FormSelect
            id="distributionSchedule"
            label="Distribution Schedule"
            value={formData.distributionSchedule}
            onChange={handleChange}
            helpText="How often profits will be distributed"
            tooltip="Choose how frequently profits will be distributed to partners."
            error={validationErrors.distributionSchedule}
          >
            <option value="">Select Schedule</option>
            <option value="monthly">Monthly</option>
            <option value="quarterly">Quarterly</option>
            <option value="semi_annually">Semi-Annually</option>
            <option value="annually">Annually</option>
            <option value="as_agreed">As Agreed by Partners</option>
          </FormSelect>
        </div>
      </div>

      {/* Financial Management */}
      <div>
        <SectionHeader 
          title="Financial Management" 
          description="Banking, accounting, and financial oversight"
          icon={FiTrendingUp}
        />
        <div className="space-y-4">
          <FormTextarea
            id="bankingFinance"
            label="Banking and Finance Arrangements"
            placeholder="Describe banking arrangements, check signing authority, financial controls..."
            value={formData.bankingFinance}
            onChange={handleChange}
            helpText="Banking arrangements and financial management procedures"
            tooltip="Specify how banking will be handled, who has check signing authority, and any financial controls."
            maxLength={300}
            rows={3}
            error={validationErrors.bankingFinance}
          />
          <FormTextarea
            id="booksAndRecords"
            label="Books and Records"
            placeholder="Describe record-keeping requirements, accounting methods, access rights..."
            value={formData.booksAndRecords}
            onChange={handleChange}
            helpText="Accounting and record-keeping requirements"
            tooltip="Specify how financial records will be maintained, accounting methods, and partner access to records."
            maxLength={300}
            rows={3}
            error={validationErrors.booksAndRecords}
          />
        </div>
      </div>
    </div>
  );
};

export default PartnershipFinancialStep;
