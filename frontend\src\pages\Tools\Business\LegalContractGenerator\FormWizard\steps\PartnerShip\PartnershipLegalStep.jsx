import React, { useCallback } from 'react';
import { FiClock, FiShield, FiAlertTriangle, FiFileText } from 'react-icons/fi';
import { FormInput, FormSelect, FormTextarea, FormCheckbox, SectionHeader } from '../../components/FormComponents';

const PartnershipLegalStep = ({ formData, onFormDataChange, validationErrors }) => {
  const handleChange = useCallback((e) => {
    const { name, value, type, checked } = e.target;
    onFormDataChange({ [name]: type === 'checkbox' ? checked : value });
  }, [onFormDataChange]);

  return (
    <div className="space-y-8">
      {/* Partnership Duration and Termination */}
      <div>
        <SectionHeader 
          title="Partnership Duration and Termination" 
          description="Timeline and termination conditions"
          icon={FiClock}
        />
        <div className="grid md:grid-cols-2 gap-6">
          <FormSelect
            id="partnershipDuration"
            label="Partnership Duration"
            value={formData.partnershipDuration}
            onChange={handleChange}
            helpText="How long the partnership will last"
            tooltip="Specify whether the partnership has a fixed term or continues indefinitely."
            error={validationErrors.partnershipDuration}
          >
            <option value="">Select Duration</option>
            <option value="indefinite">Indefinite (until terminated)</option>
            <option value="fixed_term">Fixed Term (specify end date)</option>
            <option value="project_based">Project-Based</option>
            <option value="renewable">Renewable Term</option>
          </FormSelect>
          <FormInput
            id="partnershipEndDate"
            label="End Date (if fixed term)"
            type="date"
            value={formData.partnershipEndDate}
            onChange={handleChange}
            helpText="Specific end date for fixed-term partnerships"
            tooltip="If you selected fixed term, specify when the partnership will end."
            error={validationErrors.partnershipEndDate}
          />
        </div>
        <div className="mt-4">
          <FormTextarea
            id="terminationConditions"
            label="Termination Conditions"
            placeholder="Describe conditions under which the partnership can be terminated (e.g., mutual agreement, breach of contract, death, disability, etc.)..."
            value={formData.terminationConditions}
            onChange={handleChange}
            helpText="Conditions that allow for partnership termination"
            tooltip="Specify the circumstances under which the partnership can be terminated by either party."
            maxLength={400}
            rows={3}
            error={validationErrors.terminationConditions}
          />
        </div>
      </div>

      {/* Withdrawal and Buy-Sell Provisions */}
      <div>
        <SectionHeader 
          title="Withdrawal and Buy-Sell Provisions" 
          description="Partner exit procedures and valuation methods"
          icon={FiFileText}
        />
        <div className="space-y-4">
          <FormTextarea
            id="withdrawalProcess"
            label="Withdrawal Process"
            placeholder="Describe the process for a partner to withdraw from the partnership (notice period, valuation method, payment terms, etc.)..."
            value={formData.withdrawalProcess}
            onChange={handleChange}
            helpText="Procedures for partner withdrawal from the partnership"
            tooltip="Detail the steps and requirements for a partner to exit the partnership."
            maxLength={400}
            rows={3}
            error={validationErrors.withdrawalProcess}
          />
          <FormTextarea
            id="buySellAgreement"
            label="Buy-Sell Agreement Terms"
            placeholder="Describe valuation methods, payment terms, and procedures for buying out a partner's interest..."
            value={formData.buySellAgreement}
            onChange={handleChange}
            helpText="Terms for buying out a partner's interest"
            tooltip="Specify how the partnership interest will be valued and purchased when a partner exits."
            maxLength={400}
            rows={3}
            error={validationErrors.buySellAgreement}
          />
        </div>
      </div>

      {/* Dispute Resolution */}
      <div>
        <SectionHeader 
          title="Dispute Resolution" 
          description="Methods for resolving partnership disputes"
          icon={FiShield}
        />
        <div className="grid md:grid-cols-2 gap-6">
          <FormSelect
            id="disputeResolution"
            label="Dispute Resolution Method"
            value={formData.disputeResolution}
            onChange={handleChange}
            helpText="Primary method for resolving disputes"
            tooltip="Choose the preferred method for resolving disputes between partners."
            error={validationErrors.disputeResolution}
          >
            <option value="">Select Method</option>
            <option value="mediation_arbitration">Mediation then Arbitration</option>
            <option value="arbitration_only">Arbitration Only</option>
            <option value="mediation_only">Mediation Only</option>
            <option value="court_litigation">Court Litigation</option>
            <option value="internal_process">Internal Resolution Process</option>
          </FormSelect>
          <FormInput
            id="arbitrationRules"
            label="Arbitration Rules"
            placeholder="e.g., American Arbitration Association rules"
            value={formData.arbitrationRules}
            onChange={handleChange}
            helpText="Specific arbitration rules or organization"
            tooltip="If using arbitration, specify which rules or organization will govern the process."
            maxLength={100}
            error={validationErrors.arbitrationRules}
          />
        </div>
      </div>

      {/* Additional Legal Terms */}
      <div>
        <SectionHeader 
          title="Additional Legal Terms" 
          description="Optional clauses and additional provisions"
          icon={FiAlertTriangle}
        />
        <div className="space-y-4">
          {/* Optional Clauses */}
          <div className="grid md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <FormCheckbox
                id="includeDeathDisability"
                label="Include Death/Disability Provisions"
                checked={formData.includeDeathDisability}
                onChange={handleChange}
                helpText="Add clauses for partner death or disability"
                tooltip="Include provisions for what happens if a partner dies or becomes disabled."
              />
              <FormCheckbox
                id="includeNonCompete"
                label="Include Non-Compete Clause"
                checked={formData.includeNonCompete}
                onChange={handleChange}
                helpText="Add non-compete restrictions"
                tooltip="Include clauses preventing partners from competing with the partnership business."
              />
            </div>
            <div className="space-y-3">
              <FormCheckbox
                id="includeBuySell"
                label="Include Detailed Buy-Sell Provisions"
                checked={formData.includeBuySell}
                onChange={handleChange}
                helpText="Add comprehensive buy-sell agreement"
                tooltip="Include detailed provisions for buying and selling partnership interests."
              />
              <FormCheckbox
                id="includeConfidentiality"
                label="Include Confidentiality Clause"
                checked={formData.includeConfidentiality}
                onChange={handleChange}
                helpText="Add confidentiality and non-disclosure terms"
                tooltip="Include provisions to protect confidential business information."
              />
            </div>
          </div>

          {/* Additional Terms */}
          <FormTextarea
            id="nonCompeteClause"
            label="Non-Compete Terms"
            placeholder="Describe non-compete restrictions, duration, geographic scope, etc. (if applicable)..."
            value={formData.nonCompeteClause}
            onChange={handleChange}
            helpText="Specific non-compete restrictions and terms"
            tooltip="Detail any non-compete restrictions, including duration and geographic scope."
            maxLength={400}
            rows={3}
            error={validationErrors.nonCompeteClause}
          />
          <FormTextarea
            id="confidentialityTerms"
            label="Confidentiality Terms"
            placeholder="Describe confidentiality obligations, protected information, and disclosure restrictions..."
            value={formData.confidentialityTerms}
            onChange={handleChange}
            helpText="Confidentiality and non-disclosure requirements"
            tooltip="Specify what information is confidential and the obligations to protect it."
            maxLength={400}
            rows={3}
            error={validationErrors.confidentialityTerms}
          />
          <FormTextarea
            id="additionalTerms"
            label="Additional Terms and Conditions"
            placeholder="Any additional terms, conditions, or special provisions not covered above..."
            value={formData.additionalTerms}
            onChange={handleChange}
            helpText="Any other terms or conditions to include"
            tooltip="Add any additional terms, conditions, or special provisions for your partnership."
            maxLength={500}
            rows={4}
            error={validationErrors.additionalTerms}
          />
        </div>
      </div>
    </div>
  );
};

export default PartnershipLegalStep;
