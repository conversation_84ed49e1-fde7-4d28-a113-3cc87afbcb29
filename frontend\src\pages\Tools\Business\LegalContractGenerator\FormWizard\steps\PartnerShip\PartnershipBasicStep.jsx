import React, { useCallback } from 'react';
import { FiUsers, FiGlobe, FiFileText, FiBriefcase } from 'react-icons/fi';
import { FormInput, FormSelect, FormTextarea, SectionHeader } from '../../components/FormComponents';
import { LANGUAGES, JURISDICTIONS } from '../../../utils/contractConstants';

const PartnershipBasicStep = ({ formData, onFormDataChange, validationErrors }) => {
  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    onFormDataChange({ [name]: value });
  }, [onFormDataChange]);

  // Partnership party labels
  const partyLabels = { party1: 'Partner 1', party2: 'Partner 2' };

  return (
    <div className="space-y-8">
      {/* Partnership Information */}
      <div>
        <SectionHeader 
          title="Partnership Information" 
          description="Basic details about this partnership"
          icon={FiBriefcase}
        />
        <div className="grid md:grid-cols-2 gap-6">
          <FormInput
            id="partnershipName"
            label="Partnership Name"
            placeholder="e.g., Smith & Johnson Partnership"
            value={formData.partnershipName}
            onChange={handleChange}
            helpText="The legal name of the partnership entity"
            tooltip="This will be the official name of your partnership as it appears in legal documents and business registrations."
            maxLength={100}
            error={validationErrors.partnershipName}
            required
          />
          <FormInput
            id="effectiveDate"
            label="Effective Date"
            type="date"
            value={formData.effectiveDate}
            onChange={handleChange}
            helpText="When this partnership agreement becomes effective"
            tooltip="The date when the partnership terms begin to apply. This is usually the signing date or a future start date."
            error={validationErrors.effectiveDate}
            required
          />
        </div>
        <div className="mt-4">
          <FormTextarea
            id="businessPurpose"
            label="Business Purpose"
            placeholder="Describe the main business activities and purpose of the partnership..."
            value={formData.businessPurpose}
            onChange={handleChange}
            helpText="Detailed description of the partnership's business activities and objectives"
            tooltip="Clearly define what business activities the partnership will engage in. This helps establish the scope of the partnership."
            maxLength={500}
            rows={4}
            error={validationErrors.businessPurpose}
            required
          />
        </div>
      </div>

      {/* Partner Information */}
      <div>
        <SectionHeader 
          title="Partner Information" 
          description="Details about the partnership parties"
          icon={FiUsers}
        />
        
        {/* Partner One */}
        <div className="mb-8">
          <h4 className="text-md font-semibold text-purple-400 mb-4">{partyLabels.party1} Information</h4>
          <div className="grid md:grid-cols-2 gap-6">
            <FormInput
              id="partyOneName"
              label={`${partyLabels.party1} Name`}
              placeholder="Full legal name or company name"
              value={formData.partyOneName}
              onChange={handleChange}
              helpText="Enter the full legal name or registered company name"
              tooltip="This should be the exact legal name as it appears on official documents."
              maxLength={100}
              error={validationErrors.partyOneName}
              required
            />
            <FormInput
              id="partyOneEmail"
              label="Email Address"
              type="email"
              placeholder="<EMAIL>"
              value={formData.partyOneEmail}
              onChange={handleChange}
              helpText="Primary contact email address"
              tooltip="This email will be used for partnership-related communications."
              error={validationErrors.partyOneEmail}
              required
            />
          </div>
          <div className="mt-4">
            <FormInput
              id="partyOneAddress"
              label="Address"
              placeholder="Full business or residential address"
              value={formData.partyOneAddress}
              onChange={handleChange}
              helpText="Complete address including city, state/province, and postal code"
              tooltip="Include the full address for legal identification and correspondence."
              maxLength={200}
              error={validationErrors.partyOneAddress}
              required
            />
          </div>
        </div>

        {/* Partner Two */}
        <div>
          <h4 className="text-md font-semibold text-pink-400 mb-4">{partyLabels.party2} Information</h4>
          <div className="grid md:grid-cols-2 gap-6">
            <FormInput
              id="partyTwoName"
              label={`${partyLabels.party2} Name`}
              placeholder="Full legal name or company name"
              value={formData.partyTwoName}
              onChange={handleChange}
              helpText="Enter the full legal name or registered company name"
              tooltip="This should be the exact legal name as it appears on official documents."
              maxLength={100}
              error={validationErrors.partyTwoName}
              required
            />
            <FormInput
              id="partyTwoEmail"
              label="Email Address"
              type="email"
              placeholder="<EMAIL>"
              value={formData.partyTwoEmail}
              onChange={handleChange}
              helpText="Primary contact email address"
              tooltip="This email will be used for partnership-related communications."
              error={validationErrors.partyTwoEmail}
              required
            />
          </div>
          <div className="mt-4">
            <FormInput
              id="partyTwoAddress"
              label="Address"
              placeholder="Full business or residential address"
              value={formData.partyTwoAddress}
              onChange={handleChange}
              helpText="Complete address including city, state/province, and postal code"
              tooltip="Include the full address for legal identification and correspondence."
              maxLength={200}
              error={validationErrors.partyTwoAddress}
              required
            />
          </div>
        </div>
      </div>

      {/* Legal Settings */}
      <div>
        <SectionHeader 
          title="Legal Settings" 
          description="Language and jurisdiction preferences"
          icon={FiGlobe}
        />
        <div className="grid md:grid-cols-2 gap-6">
          <FormSelect
            id="language"
            label="Contract Language"
            value={formData.language}
            onChange={handleChange}
            helpText="Language for the contract content"
            tooltip="The AI will generate the contract in your selected language with appropriate legal terminology."
            error={validationErrors.language}
            required
          >
            <option value="">Select Language</option>
            {LANGUAGES.map(lang => (
              <option key={lang.code} value={lang.code}>
                {lang.label}
              </option>
            ))}
          </FormSelect>
          
          <FormSelect
            id="jurisdiction"
            label="Governing Jurisdiction"
            value={formData.jurisdiction}
            onChange={handleChange}
            helpText="Legal jurisdiction that will govern this partnership"
            tooltip="This determines which country's or state's laws will apply to the partnership. Choose the jurisdiction most relevant to your business."
            error={validationErrors.jurisdiction}
            required
          >
            <option value="">Select Jurisdiction</option>
            {JURISDICTIONS.map(jurisdiction => (
              <option key={jurisdiction} value={jurisdiction}>
                {jurisdiction}
              </option>
            ))}
          </FormSelect>
        </div>
      </div>
    </div>
  );
};

export default PartnershipBasicStep;
