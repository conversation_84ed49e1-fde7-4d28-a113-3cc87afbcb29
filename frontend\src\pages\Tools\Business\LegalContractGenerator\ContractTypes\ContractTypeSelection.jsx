import React, { useState } from 'react';
import { FiArrowRight, FiCheck } from 'react-icons/fi';
import ContractTypeCard from './ContractTypeCard';

const ContractTypeSelection = ({ onSelectType, contractTypes }) => {
  const [selectedType, setSelectedType] = useState(null);
  const [hoveredType, setHoveredType] = useState(null);

  const handleTypeSelect = (typeId) => {
    setSelectedType(typeId);
  };

  const handleContinue = () => {
    if (selectedType && contractTypes[selectedType]) {
      onSelectType(selectedType);
    }
  };

  const contractTypesArray = Object.values(contractTypes);

  return (
    <div className="w-full max-w-6xl mx-auto px-4">
      {/* Enhanced Header with Professional Gradients */}
      <div className="text-center mb-12">
        <div className="inline-flex items-center justify-center p-4 bg-gradient-to-r from-blue-500/30 via-cyan-500/20 to-purple-500/30 rounded-2xl mb-8 backdrop-blur-sm border border-white/20 shadow-2xl shadow-blue-500/10">
          <span className="text-blue-300 font-bold text-xl">Choose Contract Type</span>
        </div>
        <h2 className="text-5xl font-bold bg-gradient-to-r from-white via-blue-200 to-purple-200 bg-clip-text text-transparent mb-6 leading-tight">
          Select Your Legal Contract
        </h2>
        <p className="text-slate-300 text-xl max-w-4xl mx-auto leading-relaxed">
          Choose the type of legal contract you need to generate. Each template is professionally crafted
          with industry-standard clauses and AI-powered customization for maximum legal compliance.
        </p>
      </div>

      {/* Contract Type Grid */}
      <div className="grid lg:grid-cols-2 xl:grid-cols-3 gap-6 mb-8">
        {contractTypesArray.map((contractType, index) => (
          <ContractTypeCard
            key={contractType.id}
            contractType={contractType}
            isSelected={selectedType === contractType.id}
            isHovered={hoveredType === contractType.id}
            onSelect={handleTypeSelect}
            onHover={setHoveredType}
            delay={index * 100}
          />
        ))}
      </div>

      {/* Enhanced Continue Button */}
      {selectedType && (
        <div className="flex justify-center animate-fade-in">
          <button
            onClick={handleContinue}
            className="group relative px-10 py-5 bg-gradient-to-r from-blue-600 via-cyan-600 to-purple-600 hover:from-blue-500 hover:via-cyan-500 hover:to-purple-500 rounded-2xl font-bold text-white text-lg transition-all duration-300 transform hover:scale-105 hover:shadow-2xl hover:shadow-blue-500/30 flex items-center gap-4 border border-white/20"
          >
            <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <FiCheck className="w-6 h-6 relative z-10" />
            <span className="relative z-10">Continue with {contractTypes[selectedType]?.title}</span>
            <FiArrowRight className="w-6 h-6 group-hover:translate-x-2 transition-transform duration-300 relative z-10" />
          </button>
        </div>
      )}

      {/* Enhanced Legal Disclaimer */}
      <div className="mt-12 p-8 bg-gradient-to-r from-yellow-500/10 via-amber-500/10 to-orange-500/10 border border-yellow-500/30 rounded-2xl backdrop-blur-sm">
        <div className="flex items-start gap-4">
          <div className="w-3 h-3 bg-gradient-to-r from-yellow-400 to-amber-400 rounded-full mt-2 flex-shrink-0 shadow-lg shadow-yellow-400/50"></div>
          <div>
            <h4 className="text-yellow-300 font-bold text-lg mb-3 flex items-center gap-2">
              <span>⚖️</span>
              Legal Disclaimer
            </h4>
            <p className="text-slate-300 leading-relaxed">
              The contracts generated by this tool are templates designed to provide a starting point for legal agreements.
              While our AI incorporates industry best practices and legal standards, we <strong className="text-yellow-300">strongly recommend</strong> having any contract
              reviewed by a qualified attorney before execution. This tool does not constitute legal advice, and Dosky is not
              responsible for the legal validity or enforceability of generated contracts.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContractTypeSelection;
