import React, { useCallback } from 'react';
import { FiUsers, FiSettings, FiUserCheck, FiTarget } from 'react-icons/fi';
import { FormInput, FormSelect, FormTextarea, SectionHeader } from '../../components/FormComponents';

const PartnershipManagementStep = ({ formData, onFormDataChange, validationErrors }) => {
  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    onFormDataChange({ [name]: value });
  }, [onFormDataChange]);

  return (
    <div className="space-y-8">
      {/* Management Structure */}
      <div>
        <SectionHeader 
          title="Management Structure" 
          description="How the partnership will be managed and operated"
          icon={FiSettings}
        />
        <div className="grid md:grid-cols-2 gap-6">
          <FormSelect
            id="managementStructure"
            label="Management Structure"
            value={formData.managementStructure}
            onChange={handleChange}
            helpText="Overall management approach for the partnership"
            tooltip="Choose how the partnership will be managed on a day-to-day basis."
            error={validationErrors.managementStructure}
          >
            <option value="">Select Structure</option>
            <option value="equal_management">Equal Management Rights</option>
            <option value="managing_partner">Designated Managing Partner</option>
            <option value="shared_responsibilities">Shared Responsibilities by Area</option>
            <option value="committee_based">Management Committee</option>
            <option value="other">Other (specify below)</option>
          </FormSelect>
          <FormSelect
            id="dailyOperations"
            label="Daily Operations Authority"
            value={formData.dailyOperations}
            onChange={handleChange}
            helpText="Who can make day-to-day operational decisions"
            tooltip="Specify who has authority to make routine business decisions."
            error={validationErrors.dailyOperations}
          >
            <option value="">Select Authority</option>
            <option value="both_partners">Both Partners Authorized</option>
            <option value="either_partner">Either Partner Can Act</option>
            <option value="managing_partner_only">Managing Partner Only</option>
            <option value="specific_limits">Specific Dollar Limits</option>
            <option value="custom">Custom Arrangement</option>
          </FormSelect>
        </div>
        <div className="mt-4">
          <FormTextarea
            id="managementDetails"
            label="Management Details"
            placeholder="Provide additional details about management structure, roles, and responsibilities..."
            value={formData.managementDetails}
            onChange={handleChange}
            helpText="Additional details about management arrangements"
            tooltip="Elaborate on the management structure, specific roles, and any special arrangements."
            maxLength={400}
            rows={3}
            error={validationErrors.managementDetails}
          />
        </div>
      </div>

      {/* Decision Making Authority */}
      <div>
        <SectionHeader 
          title="Decision Making Authority" 
          description="Voting rights and decision-making processes"
          icon={FiUserCheck}
        />
        <div className="grid md:grid-cols-2 gap-6">
          <FormSelect
            id="decisionMakingAuthority"
            label="Major Decision Authority"
            value={formData.decisionMakingAuthority}
            onChange={handleChange}
            helpText="How major business decisions will be made"
            tooltip="Specify the process for making important business decisions."
            error={validationErrors.decisionMakingAuthority}
          >
            <option value="">Select Process</option>
            <option value="unanimous_consent">Unanimous Consent Required</option>
            <option value="majority_vote">Majority Vote</option>
            <option value="weighted_voting">Weighted by Ownership</option>
            <option value="specific_partner">Specific Partner Authority</option>
            <option value="custom">Custom Voting Rules</option>
          </FormSelect>
          <FormSelect
            id="votingRights"
            label="Voting Rights"
            value={formData.votingRights}
            onChange={handleChange}
            helpText="How voting rights are allocated"
            tooltip="Specify how voting power is distributed between partners."
            error={validationErrors.votingRights}
          >
            <option value="">Select Rights</option>
            <option value="equal_voting">Equal Voting Rights</option>
            <option value="ownership_based">Based on Ownership %</option>
            <option value="contribution_based">Based on Capital Contribution</option>
            <option value="custom_allocation">Custom Allocation</option>
          </FormSelect>
        </div>
        <div className="mt-4">
          <FormTextarea
            id="majorDecisions"
            label="Major Decisions Definition"
            placeholder="Define what constitutes major decisions requiring special approval (e.g., expenditures over $10,000, hiring employees, taking loans, etc.)..."
            value={formData.majorDecisions}
            onChange={handleChange}
            helpText="Define what decisions require special approval processes"
            tooltip="Clearly specify which types of decisions require unanimous consent or special voting procedures."
            maxLength={400}
            rows={3}
            error={validationErrors.majorDecisions}
          />
        </div>
      </div>

      {/* Partner Roles and Responsibilities */}
      <div>
        <SectionHeader 
          title="Partner Roles and Responsibilities" 
          description="Specific duties and obligations of each partner"
          icon={FiTarget}
        />
        <div className="space-y-4">
          <FormTextarea
            id="partnerOneRoles"
            label="Partner 1 Roles and Responsibilities"
            placeholder="Describe specific roles, duties, and areas of responsibility for Partner 1..."
            value={formData.partnerOneRoles}
            onChange={handleChange}
            helpText="Specific duties and responsibilities of Partner 1"
            tooltip="Detail the specific roles, duties, and areas of responsibility that Partner 1 will handle."
            maxLength={400}
            rows={3}
            error={validationErrors.partnerOneRoles}
          />
          <FormTextarea
            id="partnerTwoRoles"
            label="Partner 2 Roles and Responsibilities"
            placeholder="Describe specific roles, duties, and areas of responsibility for Partner 2..."
            value={formData.partnerTwoRoles}
            onChange={handleChange}
            helpText="Specific duties and responsibilities of Partner 2"
            tooltip="Detail the specific roles, duties, and areas of responsibility that Partner 2 will handle."
            maxLength={400}
            rows={3}
            error={validationErrors.partnerTwoRoles}
          />
        </div>
      </div>

      {/* Time Commitment */}
      <div>
        <SectionHeader 
          title="Time Commitment" 
          description="Expected time dedication and availability"
          icon={FiUsers}
        />
        <div className="grid md:grid-cols-2 gap-6">
          <FormSelect
            id="timeCommitment"
            label="Time Commitment Requirement"
            value={formData.timeCommitment}
            onChange={handleChange}
            helpText="Expected time dedication to the partnership"
            tooltip="Specify the expected time commitment from each partner."
            error={validationErrors.timeCommitment}
          >
            <option value="">Select Commitment</option>
            <option value="full_time">Full-Time Dedication</option>
            <option value="part_time">Part-Time (specify hours)</option>
            <option value="as_needed">As Needed Basis</option>
            <option value="specific_schedule">Specific Schedule</option>
            <option value="flexible">Flexible Arrangement</option>
          </FormSelect>
          <FormInput
            id="workingHours"
            label="Expected Working Hours"
            placeholder="e.g., 40 hours per week, flexible schedule"
            value={formData.workingHours}
            onChange={handleChange}
            helpText="Specific working hour expectations"
            tooltip="Detail the expected working hours or schedule for partners."
            maxLength={100}
            error={validationErrors.workingHours}
          />
        </div>
      </div>
    </div>
  );
};

export default PartnershipManagementStep;
