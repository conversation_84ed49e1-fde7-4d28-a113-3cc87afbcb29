// Service Agreement PDF Generator
// Specialized PDF generation for Service Agreements

import { generateContractPdf } from './contractPdfGenerator';

/**
 * Generate PDF specifically for Service Agreements
 * @param {string} contractContent - The service agreement content
 * @param {Object} formData - Form data containing service agreement details
 * @param {string} language - Language for RTL/LTR support
 */
export const generateServiceAgreementPdf = async (contractContent, formData, language = 'English') => {
    // Format content with Service Agreement specific tags
    const formattedContent = formatServiceAgreementContent(contractContent, formData);
    
    return await generateContractPdf(formattedContent, 'service', formData, language);
};

/**
 * Format service agreement content with proper tags for PDF rendering
 */
const formatServiceAgreementContent = (content, formData) => {
    let formattedContent = content;
    
    // Add service agreement specific formatting
    formattedContent = formattedContent
        // Main title
        .replace(/^(SERVICE AGREEMENT|Service Agreement)/gm, '~H~$1')
        // Section headers
        .replace(/^(\d+\.\s*[A-Z][^.]*:?)$/gm, '~SECTION~$1')
        // Sub-sections
        .replace(/^([a-z]\)\s*[A-Z][^.]*:?)$/gm, '~H_SUB~$1')
        // Service scope items
        .replace(/^(\s*[-•]\s*.+)$/gm, '~L~$1')
        // Important clauses
        .replace(/^(WHEREAS|NOW THEREFORE|IN WITNESS WHEREOF)/gm, '~CLAUSE~$1')
        // Payment terms
        .replace(/(Payment Terms?:?)/gi, '~S~$1')
        // Deliverables
        .replace(/(Deliverables?:?)/gi, '~S~$1')
        // Timeline
        .replace(/(Timeline|Schedule|Duration):?/gi, '~S~$1')
        // Termination
        .replace(/(Termination):?/gi, '~S~$1')
        // Intellectual Property
        .replace(/(Intellectual Property|IP Rights?):?/gi, '~S~$1')
        // Confidentiality
        .replace(/(Confidentiality|Non-Disclosure):?/gi, '~S~$1')
        // Liability
        .replace(/(Liability|Limitation of Liability):?/gi, '~S~$1')
        // Governing Law
        .replace(/(Governing Law|Jurisdiction):?/gi, '~S~$1');

    // Add service-specific metadata
    const serviceMetadata = generateServiceMetadata(formData);
    formattedContent = serviceMetadata + '\n\n' + formattedContent;
    
    return formattedContent;
};

/**
 * Generate service agreement metadata section
 */
const generateServiceMetadata = (formData) => {
    const metadata = [];
    
    if (formData.serviceProvider) {
        metadata.push(`~P~Service Provider: ${formData.serviceProvider}`);
    }
    
    if (formData.client) {
        metadata.push(`~P~Client: ${formData.client}`);
    }
    
    if (formData.serviceDescription) {
        metadata.push(`~P~Service Description: ${formData.serviceDescription}`);
    }
    
    if (formData.projectValue) {
        metadata.push(`~P~Project Value: ${formData.projectValue}`);
    }
    
    if (formData.startDate) {
        metadata.push(`~P~Start Date: ${formData.startDate}`);
    }
    
    if (formData.endDate) {
        metadata.push(`~P~End Date: ${formData.endDate}`);
    }
    
    return metadata.join('\n');
};

/**
 * Service Agreement specific styling options
 */
export const serviceAgreementPdfOptions = {
    title: 'Service Agreement',
    primaryColor: '#1e40af', // Professional blue
    secondaryColor: '#059669', // Success green
    accentColor: '#d97706', // Warning amber
    fontFamily: 'Georgia',
    fontSize: {
        title: 20,
        heading: 16,
        subheading: 14,
        body: 11,
        small: 9
    },
    margins: {
        top: 20,
        bottom: 20,
        left: 15,
        right: 15
    },
    spacing: {
        afterTitle: 15,
        afterHeading: 12,
        afterSubheading: 10,
        afterParagraph: 8,
        afterListItem: 6
    }
};
