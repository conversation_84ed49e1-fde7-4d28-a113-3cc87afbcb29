import React, { useCallback } from 'react';
import { FiDollarSign, FiCalendar, FiCreditCard } from 'react-icons/fi';
import { FormInput, FormSelect, FormTextarea, SectionHeader } from '../../components/FormComponents';

const ServiceTermsStep = ({ formData, onFormDataChange, validationErrors }) => {
  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    onFormDataChange({ [name]: value });
  }, [onFormDataChange]);

  return (
    <div className="space-y-8">
      {/* Payment Information */}
      <div>
        <SectionHeader 
          title="Payment Terms" 
          description="Compensation and payment details"
          icon={FiDollarSign}
        />
        <div className="grid md:grid-cols-2 gap-6">
          <FormInput
            id="paymentAmount"
            label="Total Contract Value"
            type="number"
            placeholder="5000"
            value={formData.paymentAmount}
            onChange={handleChange}
            helpText="Total amount for the complete project (in your local currency)"
            tooltip="Enter the total project value. You can specify the currency in the payment terms section."
            error={validationErrors.paymentAmount}
            required
          />
          <FormSelect
            id="paymentSchedule"
            label="Payment Schedule"
            value={formData.paymentSchedule}
            onChange={handleChange}
            helpText="When payments will be made"
            tooltip="Choose how payments will be structured throughout the project."
            error={validationErrors.paymentSchedule}
            required
          >
            <option value="">Select Payment Schedule</option>
            <option value="upfront">100% Upfront</option>
            <option value="completion">100% Upon Completion</option>
            <option value="50-50">50% Upfront, 50% Upon Completion</option>
            <option value="30-70">30% Upfront, 70% Upon Completion</option>
            <option value="milestone">Milestone-Based Payments</option>
            <option value="monthly">Monthly Payments</option>
            <option value="custom">Custom Schedule</option>
          </FormSelect>
        </div>
      </div>

      {/* Payment Terms Details */}
      <div>
        <SectionHeader 
          title="Payment Terms Details" 
          description="Specific payment conditions and requirements"
          icon={FiCreditCard}
        />
        <FormTextarea
          id="paymentTerms"
          label="Detailed Payment Terms"
          placeholder="Specify payment methods, due dates, late fees, currency, and any other payment-related conditions..."
          value={formData.paymentTerms}
          onChange={handleChange}
          helpText="Include payment methods, due dates, late fees, and currency"
          tooltip="Be specific about payment methods accepted, invoice terms, late payment penalties, and currency."
          maxLength={500}
          rows={5}
          error={validationErrors.paymentTerms}
          required
          examples="'Payments due within 30 days of invoice. Accepted methods: bank transfer, check. Late payments subject to 1.5% monthly fee. All amounts in USD.'"
        />
      </div>

      {/* Additional Terms */}
      <div>
        <SectionHeader 
          title="Additional Terms" 
          description="Extra conditions and requirements"
          icon={FiCalendar}
        />
        <div className="space-y-6">
          <FormTextarea
            id="changeRequestPolicy"
            label="Change Request Policy (Optional)"
            placeholder="Describe how changes to the original scope will be handled..."
            value={formData.changeRequestPolicy}
            onChange={handleChange}
            helpText="How will scope changes and additional work be handled?"
            tooltip="Define the process for handling changes to the original scope, including approval and pricing."
            maxLength={300}
            rows={3}
            examples="'Changes to scope require written approval and will be billed at $100/hour. Major changes may extend timeline.'"
          />
          
          <FormTextarea
            id="cancellationPolicy"
            label="Cancellation Policy (Optional)"
            placeholder="Terms for project cancellation by either party..."
            value={formData.cancellationPolicy}
            onChange={handleChange}
            helpText="What happens if the project is cancelled?"
            tooltip="Define cancellation terms, notice periods, and any fees or refunds."
            maxLength={300}
            rows={3}
            examples="'Either party may cancel with 14 days written notice. Client pays for work completed. No refund of upfront payments.'"
          />
        </div>
      </div>

      {/* Payment Guidelines */}
      <div className="bg-green-500/10 border border-green-500/30 rounded-xl p-6">
        <h4 className="text-green-400 font-semibold mb-3 flex items-center gap-2">
          <FiDollarSign className="w-4 h-4" />
          Payment Best Practices
        </h4>
        <ul className="text-slate-300 text-sm space-y-2">
          <li className="flex items-start gap-2">
            <div className="w-1.5 h-1.5 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
            <span>Always require some payment upfront to secure commitment</span>
          </li>
          <li className="flex items-start gap-2">
            <div className="w-1.5 h-1.5 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
            <span>Tie payments to specific milestones or deliverables</span>
          </li>
          <li className="flex items-start gap-2">
            <div className="w-1.5 h-1.5 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
            <span>Include late payment fees to encourage timely payment</span>
          </li>
          <li className="flex items-start gap-2">
            <div className="w-1.5 h-1.5 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
            <span>Specify currency and exchange rate policies for international clients</span>
          </li>
        </ul>
      </div>
    </div>
  );
};

export default ServiceTermsStep;
