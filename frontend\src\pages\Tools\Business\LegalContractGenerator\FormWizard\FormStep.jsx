import React from 'react';
// Service Agreement Components
import ServiceBasicStep from '../ContractTypes/ServiceAgreement/ServiceBasicStep';
import ServiceScopeStep from './steps/Service/ServiceScopeStep';
import ServiceTermsStep from './steps/Service/ServiceTermsStep';
import ServiceLegalStep from './steps/Service/ServiceLegalStep';

// Partnership Agreement Components
import PartnershipBasicStep from '../ContractTypes/Partnership/PartnershipBasicStep';
import PartnershipFinancialStep from './steps/PartnerShip/PartnershipFinancialStep';
import PartnershipManagementStep from './steps/PartnerShip/PartnershipManagementStep';
import PartnershipLegalStep from './steps/PartnerShip/PartnershipLegalStep';

const FormStep = ({ contractType, stepId, formData, onFormDataChange, validationErrors }) => {
  const stepKey = `${contractType}_${stepId}`;

  // Map contract types and steps to their respective components
  const stepComponents = {
    // Service Agreement Steps
    service_basic: ServiceBasicStep,
    service_scope: ServiceScopeStep,
    service_terms: ServiceTermsStep,
    service_legal: ServiceLegalStep,
    // Partnership Agreement Steps
    partnership_basic: PartnershipBasicStep,
    partnership_financial: PartnershipFinancialStep,
    partnership_management: PartnershipManagementStep,
    partnership_legal: PartnershipLegalStep
  };

  const StepComponent = stepComponents[stepKey];

  if (!StepComponent) {
    return (
      <div className="text-center py-8">
        <p className="text-slate-400">Step component not found for {stepKey}</p>
      </div>
    );
  }

  return (
    <div className="animate-fade-in">
      <StepComponent
        formData={formData}
        onFormDataChange={onFormDataChange}
        validationErrors={validationErrors}
        contractType={contractType}
      />
    </div>
  );
};

export default FormStep;
