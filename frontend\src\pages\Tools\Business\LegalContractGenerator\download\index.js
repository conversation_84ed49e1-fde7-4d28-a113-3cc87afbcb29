// Legal Contract Generator Download Module
// Centralized exports for PDF generation functionality

export { generateContractPdf } from './contractPdfGenerator';
export { generateServiceAgreementPdf, serviceAgreementPdfOptions } from './serviceAgreementPdf';
export { generatePartnershipAgreementPdf, partnershipAgreementPdfOptions } from './partnershipAgreementPdf';

/**
 * Main function to generate PDF based on contract type
 * @param {string} contractContent - The contract content
 * @param {string} contractType - Type of contract (service, partnership)
 * @param {Object} formData - Form data containing contract details
 * @param {string} language - Language for RTL/LTR support
 */
export const generateContractPdfByType = async (contractContent, contractType, formData, language = 'English') => {
    switch (contractType) {
        case 'service':
            return await generateServiceAgreementPdf(contractContent, formData, language);
        case 'partnership':
            return await generatePartnershipAgreementPdf(contractContent, formData, language);
        default:
            return await generateContractPdf(contractContent, contractType, formData, language);
    }
};
